🗄️ 全图概览数据缓存系统初始化完成
✅ 阴影系统初始化完成
🎨 可视化器已设置
🖼️ 画布已设置    
🌈 配色系统已设置
💡 CAD线条: 显示/隐藏CAD原始线条
💡 CAD线条: 显示/隐藏CAD原始线条
💡 家具填充: 显示/隐藏家具填充效果
💡 墙体填充: 显示/隐藏墙体填充效果
💡 CAD线条: 显示/隐藏CAD原始线条
开始扫描文件夹: C:/A-BCXM/CAD分类标注工具C01/test-dxf-wall
📋 文件夹中有 4 个项目
  检查: wall00.bak
    文件扩展名: '.bak'
    ❌ 跳过非CAD文件: wall00.bak
  检查: wall00.dwl
    文件扩展名: '.dwl'
    ❌ 跳过非CAD文件: wall00.dwl
  检查: wall00.dwl2
    文件扩展名: '.dwl2'
    ❌ 跳过非CAD文件: wall00.dwl2
  检查: wall00.dxf
    文件扩展名: '.dxf'
    ✅ 添加CAD文件: wall00.dxf
      完整路径: C:/A-BCXM/CAD分类标注工具C01/test-dxf-wall\wall00.dxf
🎯 扫描结果: 找到 1 个CAD文件
✅ 成功找到以下CAD文件:
    wall00.dxf
📁 单文件模式: wall00.dxf - 下拉框已禁用
🚀 启动分离式多文件处理流程
============================================================
📋 阶段1：批量处理所有文件（仅显示进度，不更新界面）

📄 处理文件 1/1: wall00.dxf
  文件路径: C:/A-BCXM/CAD分类标注工具C01/test-dxf-wall\wall00.dxf
✅ 专业DXF读取器已启用 - 基于坐标系手性检测
✅ 重叠线条合并器已启用 - 门窗图层重叠线条合并
✅ 线段合并器已启用 - 墙体图层线段简化处理（迭代模式）
  📊 进度更新: 100.0% - wall00.dxf
📋 DXF版本: AC1021
📏 全局线型比例: 1000.0, 图纸空间比例: 1
📊 发现线型定义: 0 个
📊 处理图层: 24 个, 缺失线型定义: 0 个
🔧 椭圆手性校正: Z=-1.000 (LHS)
  原始参数: 2.382 - 3.927
  校正参数: 2.356 - 3.901
🔧 椭圆手性校正: Z=-1.000 (LHS)
  原始参数: -0.746 - 0.781
  校正参数: 5.503 - 0.746
🔧 椭圆手性校正: Z=-1.000 (LHS)
  原始参数: 3.923 - 5.395
  校正参数: 0.888 - 2.360
🔧 椭圆手性校正: Z=-1.000 (LHS)
  原始参数: 2.382 - 3.927
  校正参数: 2.356 - 3.901

============================================================
🔍 专业DXF读取器 - 坐标系手性校正摘要
============================================================
总实体数: 484
圆弧实体: 0 (校正: 0)
椭圆实体: 10 (校正: 4)
右手坐标系 (RHS): 6
左手坐标系 (LHS): 4

📐 校正详情:
  ELLIPSE (左手坐标系):
    原始参数: (2.381838326332434, 3.926990816987268)
    校正参数: (2.356194490192318, 3.901346980847152)
  ELLIPSE (左手坐标系):
    原始参数: (-0.7461410203456446, 0.7805771262022079)
    校正参数: (5.502608180977378, 0.7461410203456449)
  ELLIPSE (左手坐标系):
    原始参数: (3.923323364970616, 5.394767999918641)
    校正参数: (0.8884173072609451, 2.3598619422089704)
  ELLIPSE (左手坐标系):
    原始参数: (2.381838326332442, 3.92699081698726)
    校正参数: (2.356194490192326, 3.901346980847144)
============================================================
🔧 开始墙体图层线段合并处理...
🔧 开始线段合并处理...
  识别到墙体线条: 330 条
  其他实体: 154 个
🔄 开始迭代合并，最大迭代次数: 3
  📍 迭代 1: 输入线段数量 330
    ✅ 迭代 1 完成: 330 -> 330 (合并了 0 条)
    🎯 迭代 1 后无更多合并，停止迭代
🎉 迭代合并完成: 总共 1 次迭代

📊 线段合并统计:
  原始线段数量: 330
  合并线段数量: 0
  最终线段数量: 330
  处理时间: 0.032秒
  简化率: 0.0%
  迭代次数: 1
  迭代详情:
    迭代 1: 330 -> 330 (合并 0 条)
✅ 线段合并完成: 484 -> 484 个实体
🔧 开始重叠线条合并处理...
   门窗图层实体: 154 个
   其他图层实体: 330 个
   处理 144 个LINE实体...
   处理图层 'A-WINDOW': 144 个线条
     图层 'A-WINDOW' 合并了 6 个重叠线条
✅ 重叠线条合并完成: 484 → 478 个实体 (合并了 6 个重叠线条)
特殊图层识别结果:
  墙体图层: {'A-WALL'}
  门窗图层: {'A-WINDOW'}
  栏杆图层: set()
去重: 330 → 1 个实体
  ⚠️ 没有分组数据，无法更新组信息
跳过后台处理的自动标注可视化更新
  未处理实体数量: 329
    调试：输入实体类型: [<class 'dict'>, <class 'dict'>, <class 'dict'>]
    调试：输入实体示例: {'type': 'LINE', 'layer': 'A-WALL', 'start_x': 0, 'start_y': 0, 'end_x': 0, 'end_y': 0, 'color': 256, 'linetype': 'Continuous', 'merged': True}
去重: 329 → 1 个实体
  数据结构统一化完成: 1个字典格式的组
  其他实体分组结果: 1个组
    调试：组 0 类型: <class 'dict'>
    调试：组 0 键: ['entities', 'label', 'group_type', 'layer', 'status', 'confidence']
  有效分组数量: 1个组
  开始优化分组: 1个其他组, 32个自动组
  优化完成: 1个组
调用强制合并SPLINE实体，当前组数: 33
错误详情: Traceback (most recent call last):
  File "c:\A-BCXM\CAD分类标注工具E02\main_enhanced.py", line 205, in process_single_file
    self.all_groups = self.processor._force_merge_spline_entities(self.all_groups)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\A-BCXM\CAD分类标注工具E02\cad_data_processor.py", line 3923, in _force_merge_spline_entities
    spline_in_group = [e for e in group if e.get('type') == 'SPLINE']
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\A-BCXM\CAD分类标注工具E02\cad_data_processor.py", line 3923, in <listcomp>
    spline_in_group = [e for e in group if e.get('type') == 'SPLINE']
                                           ^^^^^
AttributeError: 'str' object has no attribute 'get'

  处理器返回失败
  ❌ 处理失败

============================================================
📋 阶段1完成：批量处理结果
  ✅ 成功处理: 0 个文件
  ❌ 处理失败: 1 个文件
  失败文件列表:
    - wall00.dxf